import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, CircularProgress } from '@mui/material';
import { useAuth } from './contexts/AuthContext';
import Layout from './components/Layout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Members from './pages/Members';
import Income from './pages/Income';
import Expenses from './pages/Expenses';
import Programs from './pages/Programs';
import Loans from './pages/Loans';
import Reports from './pages/Reports';
import Settings from './pages/Settings';

// مكون للحماية - يتطلب تسجيل الدخول
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
      </Box>
    );
  }
  
  return isAuthenticated ? children : <Navigate to="/login" replace />;
};

// مكون للحماية - يتطلب صلاحيات معينة
const RoleProtectedRoute = ({ children, requiredRole }) => {
  const { user, isAuthenticated, loading } = useAuth();
  
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
      </Box>
    );
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  // التحقق من الصلاحيات
  const hasPermission = () => {
    switch (requiredRole) {
      case 'admin':
        return user.role === 'admin';
      case 'treasurer':
        return user.role === 'admin' || user.role === 'treasurer';
      case 'employee':
        return user.role === 'admin' || user.role === 'treasurer' || user.role === 'employee';
      default:
        return true;
    }
  };
  
  return hasPermission() ? children : <Navigate to="/dashboard" replace />;
};

function App() {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Routes>
      {/* صفحة تسجيل الدخول */}
      <Route 
        path="/login" 
        element={
          isAuthenticated ? <Navigate to="/dashboard" replace /> : <Login />
        } 
      />
      
      {/* الصفحات المحمية */}
      <Route path="/" element={
        <ProtectedRoute>
          <Layout />
        </ProtectedRoute>
      }>
        {/* الصفحة الرئيسية */}
        <Route index element={<Navigate to="/dashboard" replace />} />
        <Route path="dashboard" element={<Dashboard />} />
        
        {/* إدارة المستفيدين */}
        <Route path="members" element={
          <RoleProtectedRoute requiredRole="employee">
            <Members />
          </RoleProtectedRoute>
        } />
        
        {/* إدارة الدخل */}
        <Route path="income" element={
          <RoleProtectedRoute requiredRole="employee">
            <Income />
          </RoleProtectedRoute>
        } />
        
        {/* إدارة المصروفات */}
        <Route path="expenses" element={
          <RoleProtectedRoute requiredRole="employee">
            <Expenses />
          </RoleProtectedRoute>
        } />
        
        {/* إدارة البرامج */}
        <Route path="programs" element={
          <RoleProtectedRoute requiredRole="employee">
            <Programs />
          </RoleProtectedRoute>
        } />
        
        {/* إدارة السلف */}
        <Route path="loans" element={
          <RoleProtectedRoute requiredRole="employee">
            <Loans />
          </RoleProtectedRoute>
        } />
        
        {/* التقارير */}
        <Route path="reports" element={
          <RoleProtectedRoute requiredRole="employee">
            <Reports />
          </RoleProtectedRoute>
        } />
        
        {/* الإعدادات */}
        <Route path="settings" element={<Settings />} />
      </Route>
      
      {/* صفحة غير موجودة */}
      <Route path="*" element={<Navigate to="/dashboard" replace />} />
    </Routes>
  );
}

export default App;
