{"name": "sosiel2025", "version": "1.0.0", "description": "نظام إدارة اللجنة الاجتماعية للنقابة", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm run dev", "build": "cd client && npm run build", "start": "cd server && npm start", "install-all": "npm install && cd server && npm install && cd ../client && npm install"}, "keywords": ["نقابة", "لجنة اجتماعية", "إدارة"], "author": "نظام إدارة اللجنة الاجتماعية", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}