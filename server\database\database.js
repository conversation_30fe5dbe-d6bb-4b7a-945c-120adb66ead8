const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
const dbDir = path.dirname(__filename);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

const dbPath = path.join(__dirname, 'sosiel.db');

class Database {
  constructor() {
    this.db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('خطأ في الاتصال بقاعدة البيانات:', err.message);
      } else {
        console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
        this.initializeTables();
      }
    });
  }

  initializeTables() {
    // جدول المستخدمين
    this.db.run(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        full_name TEXT NOT NULL,
        role TEXT NOT NULL CHECK(role IN ('admin', 'treasurer', 'employee')),
        email TEXT,
        phone TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول المستفيدين
    this.db.run(`
      CREATE TABLE IF NOT EXISTS members (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id TEXT UNIQUE NOT NULL,
        full_name TEXT NOT NULL,
        type TEXT NOT NULL CHECK(type IN ('employee', 'retired', 'beneficiary')),
        department TEXT,
        position TEXT,
        hire_date DATE,
        retirement_date DATE,
        phone TEXT,
        email TEXT,
        address TEXT,
        national_id TEXT,
        bank_account TEXT,
        emergency_contact TEXT,
        emergency_phone TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول مصادر الدخل
    this.db.run(`
      CREATE TABLE IF NOT EXISTS income (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL CHECK(type IN ('subscription', 'government_aid', 'institution_contribution', 'donation', 'bank_interest', 'other')),
        amount DECIMAL(10,2) NOT NULL,
        description TEXT,
        source TEXT,
        date DATE NOT NULL,
        member_id INTEGER,
        receipt_number TEXT,
        created_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (member_id) REFERENCES members(id),
        FOREIGN KEY (created_by) REFERENCES users(id)
      )
    `);

    // جدول المصروفات الاجتماعية
    this.db.run(`
      CREATE TABLE IF NOT EXISTS expenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL CHECK(type IN ('retirement_grant', 'death_grant', 'marriage_grant', 'emergency_aid', 'event_expense', 'other')),
        amount DECIMAL(10,2) NOT NULL,
        description TEXT,
        member_id INTEGER NOT NULL,
        beneficiary_name TEXT,
        date DATE NOT NULL,
        status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'approved', 'paid', 'rejected')),
        approved_by INTEGER,
        paid_by INTEGER,
        receipt_number TEXT,
        notes TEXT,
        created_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (member_id) REFERENCES members(id),
        FOREIGN KEY (approved_by) REFERENCES users(id),
        FOREIGN KEY (paid_by) REFERENCES users(id),
        FOREIGN KEY (created_by) REFERENCES users(id)
      )
    `);

    // جدول البرامج (الاصطياف والعمرة)
    this.db.run(`
      CREATE TABLE IF NOT EXISTS programs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        type TEXT NOT NULL CHECK(type IN ('summer', 'umrah', 'cultural', 'sports', 'religious', 'other')),
        description TEXT,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        location TEXT,
        max_participants INTEGER,
        cost_per_person DECIMAL(10,2),
        total_budget DECIMAL(10,2),
        status TEXT DEFAULT 'planning' CHECK(status IN ('planning', 'registration_open', 'registration_closed', 'in_progress', 'completed', 'cancelled')),
        created_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id)
      )
    `);

    // جدول المشاركين في البرامج
    this.db.run(`
      CREATE TABLE IF NOT EXISTS program_participants (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        program_id INTEGER NOT NULL,
        member_id INTEGER NOT NULL,
        registration_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        status TEXT DEFAULT 'registered' CHECK(status IN ('registered', 'confirmed', 'cancelled', 'attended')),
        payment_status TEXT DEFAULT 'pending' CHECK(payment_status IN ('pending', 'partial', 'paid', 'refunded')),
        amount_paid DECIMAL(10,2) DEFAULT 0,
        notes TEXT,
        FOREIGN KEY (program_id) REFERENCES programs(id),
        FOREIGN KEY (member_id) REFERENCES members(id),
        UNIQUE(program_id, member_id)
      )
    `);

    // جدول السلف المالية
    this.db.run(`
      CREATE TABLE IF NOT EXISTS loans (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        member_id INTEGER NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        purpose TEXT,
        request_date DATE NOT NULL,
        approval_date DATE,
        due_date DATE,
        status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'approved', 'disbursed', 'repaid', 'overdue', 'rejected')),
        monthly_deduction DECIMAL(10,2),
        total_repaid DECIMAL(10,2) DEFAULT 0,
        remaining_balance DECIMAL(10,2),
        approved_by INTEGER,
        disbursed_by INTEGER,
        notes TEXT,
        created_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (member_id) REFERENCES members(id),
        FOREIGN KEY (approved_by) REFERENCES users(id),
        FOREIGN KEY (disbursed_by) REFERENCES users(id),
        FOREIGN KEY (created_by) REFERENCES users(id)
      )
    `);

    // جدول دفعات السلف
    this.db.run(`
      CREATE TABLE IF NOT EXISTS loan_payments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        loan_id INTEGER NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_date DATE NOT NULL,
        payment_method TEXT DEFAULT 'salary_deduction',
        receipt_number TEXT,
        notes TEXT,
        created_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (loan_id) REFERENCES loans(id),
        FOREIGN KEY (created_by) REFERENCES users(id)
      )
    `);
  }

  // طريقة للحصول على اتصال قاعدة البيانات
  getConnection() {
    return this.db;
  }

  // إغلاق الاتصال
  close() {
    return new Promise((resolve, reject) => {
      this.db.close((err) => {
        if (err) {
          reject(err);
        } else {
          console.log('تم إغلاق اتصال قاعدة البيانات');
          resolve();
        }
      });
    });
  }
}

module.exports = new Database();
