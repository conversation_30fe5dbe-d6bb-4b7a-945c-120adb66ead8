const jwt = require('jsonwebtoken');
const database = require('../database/database');

// التحقق من صحة الرمز المميز
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'مطلوب رمز الوصول' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'رمز الوصول غير صالح' });
    }
    req.user = user;
    next();
  });
};

// التحقق من الصلاحيات
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: 'غير مصرح بالوصول' });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ error: 'ليس لديك صلاحية للوصول لهذا المورد' });
    }

    next();
  };
};

// التحقق من صلاحية الإدارة
const requireAdmin = requireRole(['admin']);

// التحقق من صلاحية أمين المال أو الإدارة
const requireTreasurer = requireRole(['admin', 'treasurer']);

// التحقق من أي صلاحية (موظف أو أعلى)
const requireEmployee = requireRole(['admin', 'treasurer', 'employee']);

module.exports = {
  authenticateToken,
  requireRole,
  requireAdmin,
  requireTreasurer,
  requireEmployee
};
