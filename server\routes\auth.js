const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const database = require('../database/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const db = database.getConnection();

// تسجيل الدخول
router.post('/login', (req, res) => {
  const { username, password } = req.body;

  if (!username || !password) {
    return res.status(400).json({ error: 'اسم المستخدم وكلمة المرور مطلوبان' });
  }

  const query = 'SELECT * FROM users WHERE username = ? AND is_active = 1';
  
  db.get(query, [username], async (err, user) => {
    if (err) {
      console.error('خطأ في قاعدة البيانات:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }

    if (!user) {
      return res.status(401).json({ error: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }

    try {
      const isValidPassword = await bcrypt.compare(password, user.password);
      
      if (!isValidPassword) {
        return res.status(401).json({ error: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
      }

      const token = jwt.sign(
        { 
          id: user.id, 
          username: user.username, 
          role: user.role,
          full_name: user.full_name
        },
        process.env.JWT_SECRET,
        { expiresIn: '24h' }
      );

      // تحديث وقت آخر دخول
      const updateQuery = 'UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = ?';
      db.run(updateQuery, [user.id]);

      res.json({
        message: 'تم تسجيل الدخول بنجاح',
        token,
        user: {
          id: user.id,
          username: user.username,
          full_name: user.full_name,
          role: user.role,
          email: user.email
        }
      });
    } catch (error) {
      console.error('خطأ في التشفير:', error);
      res.status(500).json({ error: 'خطأ في الخادم' });
    }
  });
});

// الحصول على معلومات المستخدم الحالي
router.get('/me', authenticateToken, (req, res) => {
  const query = 'SELECT id, username, full_name, role, email, phone FROM users WHERE id = ?';
  
  db.get(query, [req.user.id], (err, user) => {
    if (err) {
      console.error('خطأ في قاعدة البيانات:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }

    if (!user) {
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }

    res.json({ user });
  });
});

// تغيير كلمة المرور
router.post('/change-password', authenticateToken, async (req, res) => {
  const { currentPassword, newPassword } = req.body;

  if (!currentPassword || !newPassword) {
    return res.status(400).json({ error: 'كلمة المرور الحالية والجديدة مطلوبتان' });
  }

  if (newPassword.length < 6) {
    return res.status(400).json({ error: 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل' });
  }

  const query = 'SELECT password FROM users WHERE id = ?';
  
  db.get(query, [req.user.id], async (err, user) => {
    if (err) {
      console.error('خطأ في قاعدة البيانات:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }

    try {
      const isValidPassword = await bcrypt.compare(currentPassword, user.password);
      
      if (!isValidPassword) {
        return res.status(401).json({ error: 'كلمة المرور الحالية غير صحيحة' });
      }

      const hashedNewPassword = await bcrypt.hash(newPassword, 10);
      const updateQuery = 'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
      
      db.run(updateQuery, [hashedNewPassword, req.user.id], function(err) {
        if (err) {
          console.error('خطأ في تحديث كلمة المرور:', err);
          return res.status(500).json({ error: 'خطأ في تحديث كلمة المرور' });
        }

        res.json({ message: 'تم تغيير كلمة المرور بنجاح' });
      });
    } catch (error) {
      console.error('خطأ في التشفير:', error);
      res.status(500).json({ error: 'خطأ في الخادم' });
    }
  });
});

module.exports = router;
