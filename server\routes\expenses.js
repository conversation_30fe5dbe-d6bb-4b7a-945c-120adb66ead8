const express = require('express');
const database = require('../database/database');
const { authenticateToken, requireEmployee, requireTreasurer, requireAdmin } = require('../middleware/auth');

const router = express.Router();
const db = database.getConnection();

// الحصول على جميع المصروفات
router.get('/', authenticateToken, requireEmployee, (req, res) => {
  const { type, status, start_date, end_date, page = 1, limit = 50 } = req.query;
  const offset = (page - 1) * limit;
  
  let query = `
    SELECT e.*, m.full_name as member_name, m.employee_id,
           u1.full_name as created_by_name,
           u2.full_name as approved_by_name,
           u3.full_name as paid_by_name
    FROM expenses e
    LEFT JOIN members m ON e.member_id = m.id
    LEFT JOIN users u1 ON e.created_by = u1.id
    LEFT JOIN users u2 ON e.approved_by = u2.id
    LEFT JOIN users u3 ON e.paid_by = u3.id
    WHERE 1=1
  `;
  let params = [];
  
  if (type) {
    query += ' AND e.type = ?';
    params.push(type);
  }
  
  if (status) {
    query += ' AND e.status = ?';
    params.push(status);
  }
  
  if (start_date) {
    query += ' AND e.date >= ?';
    params.push(start_date);
  }
  
  if (end_date) {
    query += ' AND e.date <= ?';
    params.push(end_date);
  }
  
  query += ' ORDER BY e.date DESC, e.created_at DESC LIMIT ? OFFSET ?';
  params.push(parseInt(limit), parseInt(offset));
  
  db.all(query, params, (err, expenses) => {
    if (err) {
      console.error('خطأ في قاعدة البيانات:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    // الحصول على العدد الإجمالي والمجموع
    let countQuery = 'SELECT COUNT(*) as total, SUM(amount) as total_amount FROM expenses WHERE 1=1';
    let countParams = [];
    
    if (type) {
      countQuery += ' AND type = ?';
      countParams.push(type);
    }
    
    if (status) {
      countQuery += ' AND status = ?';
      countParams.push(status);
    }
    
    if (start_date) {
      countQuery += ' AND date >= ?';
      countParams.push(start_date);
    }
    
    if (end_date) {
      countQuery += ' AND date <= ?';
      countParams.push(end_date);
    }
    
    db.get(countQuery, countParams, (err, countResult) => {
      if (err) {
        console.error('خطأ في عد المصروفات:', err);
        return res.status(500).json({ error: 'خطأ في الخادم' });
      }
      
      res.json({
        expenses,
        summary: {
          total_records: countResult.total,
          total_amount: countResult.total_amount || 0
        },
        pagination: {
          total: countResult.total,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(countResult.total / limit)
        }
      });
    });
  });
});

// إضافة مصروف جديد
router.post('/', authenticateToken, requireEmployee, (req, res) => {
  const { type, amount, description, member_id, beneficiary_name, date, notes } = req.body;
  
  if (!type || !amount || !member_id || !date) {
    return res.status(400).json({ error: 'نوع المصروف والمبلغ والمستفيد والتاريخ مطلوبة' });
  }
  
  if (amount <= 0) {
    return res.status(400).json({ error: 'المبلغ يجب أن يكون أكبر من صفر' });
  }
  
  const query = `
    INSERT INTO expenses (type, amount, description, member_id, beneficiary_name, date, notes, created_by)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `;
  
  const params = [type, amount, description, member_id, beneficiary_name, date, notes, req.user.id];
  
  db.run(query, params, function(err) {
    if (err) {
      console.error('خطأ في إضافة المصروف:', err);
      return res.status(500).json({ error: 'خطأ في إضافة المصروف' });
    }
    
    res.status(201).json({
      message: 'تم إضافة المصروف بنجاح',
      expense: { id: this.lastID, ...req.body, created_by: req.user.id, status: 'pending' }
    });
  });
});

// الموافقة على مصروف
router.patch('/:id/approve', authenticateToken, requireTreasurer, (req, res) => {
  const { notes } = req.body;
  
  const query = `
    UPDATE expenses SET 
      status = 'approved', 
      approved_by = ?, 
      approval_date = CURRENT_DATE,
      notes = COALESCE(?, notes)
    WHERE id = ? AND status = 'pending'
  `;
  
  db.run(query, [req.user.id, notes, req.params.id], function(err) {
    if (err) {
      console.error('خطأ في الموافقة على المصروف:', err);
      return res.status(500).json({ error: 'خطأ في الموافقة على المصروف' });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'المصروف غير موجود أو تم الموافقة عليه مسبقاً' });
    }
    
    res.json({ message: 'تم الموافقة على المصروف بنجاح' });
  });
});

// رفض مصروف
router.patch('/:id/reject', authenticateToken, requireTreasurer, (req, res) => {
  const { notes } = req.body;
  
  if (!notes) {
    return res.status(400).json({ error: 'سبب الرفض مطلوب' });
  }
  
  const query = `
    UPDATE expenses SET 
      status = 'rejected', 
      approved_by = ?, 
      notes = ?
    WHERE id = ? AND status = 'pending'
  `;
  
  db.run(query, [req.user.id, notes, req.params.id], function(err) {
    if (err) {
      console.error('خطأ في رفض المصروف:', err);
      return res.status(500).json({ error: 'خطأ في رفض المصروف' });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'المصروف غير موجود أو تم التعامل معه مسبقاً' });
    }
    
    res.json({ message: 'تم رفض المصروف' });
  });
});

// تسديد مصروف
router.patch('/:id/pay', authenticateToken, requireTreasurer, (req, res) => {
  const { receipt_number, notes } = req.body;
  
  const query = `
    UPDATE expenses SET 
      status = 'paid', 
      paid_by = ?, 
      receipt_number = ?,
      notes = COALESCE(?, notes)
    WHERE id = ? AND status = 'approved'
  `;
  
  db.run(query, [req.user.id, receipt_number, notes, req.params.id], function(err) {
    if (err) {
      console.error('خطأ في تسديد المصروف:', err);
      return res.status(500).json({ error: 'خطأ في تسديد المصروف' });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'المصروف غير موجود أو غير موافق عليه' });
    }
    
    res.json({ message: 'تم تسديد المصروف بنجاح' });
  });
});

// تحديث مصروف
router.put('/:id', authenticateToken, requireTreasurer, (req, res) => {
  const { type, amount, description, member_id, beneficiary_name, date, notes } = req.body;
  
  // التحقق من أن المصروف لم يتم دفعه بعد
  const checkQuery = 'SELECT status FROM expenses WHERE id = ?';
  
  db.get(checkQuery, [req.params.id], (err, expense) => {
    if (err) {
      console.error('خطأ في التحقق من المصروف:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    if (!expense) {
      return res.status(404).json({ error: 'المصروف غير موجود' });
    }
    
    if (expense.status === 'paid') {
      return res.status(400).json({ error: 'لا يمكن تعديل مصروف تم دفعه' });
    }
    
    const updateQuery = `
      UPDATE expenses SET
        type = ?, amount = ?, description = ?, member_id = ?, 
        beneficiary_name = ?, date = ?, notes = ?
      WHERE id = ?
    `;
    
    const params = [type, amount, description, member_id, beneficiary_name, date, notes, req.params.id];
    
    db.run(updateQuery, params, function(err) {
      if (err) {
        console.error('خطأ في تحديث المصروف:', err);
        return res.status(500).json({ error: 'خطأ في تحديث المصروف' });
      }
      
      res.json({ message: 'تم تحديث المصروف بنجاح' });
    });
  });
});

// حذف مصروف
router.delete('/:id', authenticateToken, requireAdmin, (req, res) => {
  const query = 'DELETE FROM expenses WHERE id = ? AND status IN ("pending", "rejected")';
  
  db.run(query, [req.params.id], function(err) {
    if (err) {
      console.error('خطأ في حذف المصروف:', err);
      return res.status(500).json({ error: 'خطأ في حذف المصروف' });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'المصروف غير موجود أو لا يمكن حذفه' });
    }
    
    res.json({ message: 'تم حذف المصروف بنجاح' });
  });
});

module.exports = router;
