const express = require('express');
const database = require('../database/database');
const { authenticateToken, requireEmployee, requireTreasurer } = require('../middleware/auth');

const router = express.Router();
const db = database.getConnection();

// الحصول على جميع مصادر الدخل
router.get('/', authenticateToken, requireEmployee, (req, res) => {
  const { type, start_date, end_date, page = 1, limit = 50 } = req.query;
  const offset = (page - 1) * limit;
  
  let query = `
    SELECT i.*, m.full_name as member_name, u.full_name as created_by_name
    FROM income i
    LEFT JOIN members m ON i.member_id = m.id
    LEFT JOIN users u ON i.created_by = u.id
    WHERE 1=1
  `;
  let params = [];
  
  if (type) {
    query += ' AND i.type = ?';
    params.push(type);
  }
  
  if (start_date) {
    query += ' AND i.date >= ?';
    params.push(start_date);
  }
  
  if (end_date) {
    query += ' AND i.date <= ?';
    params.push(end_date);
  }
  
  query += ' ORDER BY i.date DESC, i.created_at DESC LIMIT ? OFFSET ?';
  params.push(parseInt(limit), parseInt(offset));
  
  db.all(query, params, (err, incomes) => {
    if (err) {
      console.error('خطأ في قاعدة البيانات:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    // الحصول على العدد الإجمالي والمجموع
    let countQuery = 'SELECT COUNT(*) as total, SUM(amount) as total_amount FROM income WHERE 1=1';
    let countParams = [];
    
    if (type) {
      countQuery += ' AND type = ?';
      countParams.push(type);
    }
    
    if (start_date) {
      countQuery += ' AND date >= ?';
      countParams.push(start_date);
    }
    
    if (end_date) {
      countQuery += ' AND date <= ?';
      countParams.push(end_date);
    }
    
    db.get(countQuery, countParams, (err, countResult) => {
      if (err) {
        console.error('خطأ في عد الدخل:', err);
        return res.status(500).json({ error: 'خطأ في الخادم' });
      }
      
      res.json({
        incomes,
        summary: {
          total_records: countResult.total,
          total_amount: countResult.total_amount || 0
        },
        pagination: {
          total: countResult.total,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(countResult.total / limit)
        }
      });
    });
  });
});

// إضافة مصدر دخل جديد
router.post('/', authenticateToken, requireTreasurer, (req, res) => {
  const { type, amount, description, source, date, member_id, receipt_number } = req.body;
  
  if (!type || !amount || !date) {
    return res.status(400).json({ error: 'نوع الدخل والمبلغ والتاريخ مطلوبة' });
  }
  
  if (amount <= 0) {
    return res.status(400).json({ error: 'المبلغ يجب أن يكون أكبر من صفر' });
  }
  
  const query = `
    INSERT INTO income (type, amount, description, source, date, member_id, receipt_number, created_by)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `;
  
  const params = [type, amount, description, source, date, member_id, receipt_number, req.user.id];
  
  db.run(query, params, function(err) {
    if (err) {
      console.error('خطأ في إضافة الدخل:', err);
      return res.status(500).json({ error: 'خطأ في إضافة الدخل' });
    }
    
    res.status(201).json({
      message: 'تم إضافة مصدر الدخل بنجاح',
      income: { id: this.lastID, ...req.body, created_by: req.user.id }
    });
  });
});

// تحديث مصدر دخل
router.put('/:id', authenticateToken, requireTreasurer, (req, res) => {
  const { type, amount, description, source, date, member_id, receipt_number } = req.body;
  
  if (!type || !amount || !date) {
    return res.status(400).json({ error: 'نوع الدخل والمبلغ والتاريخ مطلوبة' });
  }
  
  if (amount <= 0) {
    return res.status(400).json({ error: 'المبلغ يجب أن يكون أكبر من صفر' });
  }
  
  const query = `
    UPDATE income SET
      type = ?, amount = ?, description = ?, source = ?, date = ?,
      member_id = ?, receipt_number = ?
    WHERE id = ?
  `;
  
  const params = [type, amount, description, source, date, member_id, receipt_number, req.params.id];
  
  db.run(query, params, function(err) {
    if (err) {
      console.error('خطأ في تحديث الدخل:', err);
      return res.status(500).json({ error: 'خطأ في تحديث الدخل' });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'مصدر الدخل غير موجود' });
    }
    
    res.json({ message: 'تم تحديث مصدر الدخل بنجاح' });
  });
});

// حذف مصدر دخل
router.delete('/:id', authenticateToken, requireTreasurer, (req, res) => {
  const query = 'DELETE FROM income WHERE id = ?';
  
  db.run(query, [req.params.id], function(err) {
    if (err) {
      console.error('خطأ في حذف الدخل:', err);
      return res.status(500).json({ error: 'خطأ في حذف الدخل' });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'مصدر الدخل غير موجود' });
    }
    
    res.json({ message: 'تم حذف مصدر الدخل بنجاح' });
  });
});

// إحصائيات الدخل
router.get('/stats', authenticateToken, requireEmployee, (req, res) => {
  const { year = new Date().getFullYear() } = req.query;
  
  const query = `
    SELECT 
      type,
      COUNT(*) as count,
      SUM(amount) as total_amount,
      AVG(amount) as avg_amount
    FROM income 
    WHERE strftime('%Y', date) = ?
    GROUP BY type
    ORDER BY total_amount DESC
  `;
  
  db.all(query, [year.toString()], (err, stats) => {
    if (err) {
      console.error('خطأ في إحصائيات الدخل:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    // إحصائيات شهرية
    const monthlyQuery = `
      SELECT 
        strftime('%m', date) as month,
        SUM(amount) as total_amount,
        COUNT(*) as count
      FROM income 
      WHERE strftime('%Y', date) = ?
      GROUP BY strftime('%m', date)
      ORDER BY month
    `;
    
    db.all(monthlyQuery, [year.toString()], (err, monthlyStats) => {
      if (err) {
        console.error('خطأ في الإحصائيات الشهرية:', err);
        return res.status(500).json({ error: 'خطأ في الخادم' });
      }
      
      res.json({
        year: parseInt(year),
        by_type: stats,
        by_month: monthlyStats
      });
    });
  });
});

module.exports = router;
