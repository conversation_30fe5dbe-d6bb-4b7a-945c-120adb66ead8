const express = require('express');
const database = require('../database/database');
const { authenticateToken, requireEmployee, requireTreasurer } = require('../middleware/auth');

const router = express.Router();
const db = database.getConnection();

// الحصول على جميع السلف
router.get('/', authenticateToken, requireEmployee, (req, res) => {
  const { status, member_id, page = 1, limit = 50 } = req.query;
  const offset = (page - 1) * limit;
  
  let query = `
    SELECT l.*, m.full_name as member_name, m.employee_id,
           u1.full_name as created_by_name,
           u2.full_name as approved_by_name,
           u3.full_name as disbursed_by_name
    FROM loans l
    LEFT JOIN members m ON l.member_id = m.id
    LEFT JOIN users u1 ON l.created_by = u1.id
    LEFT JOIN users u2 ON l.approved_by = u2.id
    LEFT JOIN users u3 ON l.disbursed_by = u3.id
    WHERE 1=1
  `;
  let params = [];
  
  if (status) {
    query += ' AND l.status = ?';
    params.push(status);
  }
  
  if (member_id) {
    query += ' AND l.member_id = ?';
    params.push(member_id);
  }
  
  query += ' ORDER BY l.request_date DESC LIMIT ? OFFSET ?';
  params.push(parseInt(limit), parseInt(offset));
  
  db.all(query, params, (err, loans) => {
    if (err) {
      console.error('خطأ في قاعدة البيانات:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    res.json({ loans });
  });
});

// الحصول على سلفة واحدة مع تفاصيل الدفعات
router.get('/:id', authenticateToken, requireEmployee, (req, res) => {
  const loanQuery = `
    SELECT l.*, m.full_name as member_name, m.employee_id
    FROM loans l
    JOIN members m ON l.member_id = m.id
    WHERE l.id = ?
  `;
  
  db.get(loanQuery, [req.params.id], (err, loan) => {
    if (err) {
      console.error('خطأ في قاعدة البيانات:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    if (!loan) {
      return res.status(404).json({ error: 'السلفة غير موجودة' });
    }
    
    // الحصول على دفعات السداد
    const paymentsQuery = `
      SELECT lp.*, u.full_name as created_by_name
      FROM loan_payments lp
      LEFT JOIN users u ON lp.created_by = u.id
      WHERE lp.loan_id = ?
      ORDER BY lp.payment_date DESC
    `;
    
    db.all(paymentsQuery, [req.params.id], (err, payments) => {
      if (err) {
        console.error('خطأ في الحصول على الدفعات:', err);
        return res.status(500).json({ error: 'خطأ في الخادم' });
      }
      
      res.json({ loan, payments });
    });
  });
});

// طلب سلفة جديدة
router.post('/', authenticateToken, requireEmployee, (req, res) => {
  const { member_id, amount, purpose, due_date, monthly_deduction } = req.body;
  
  if (!member_id || !amount || !purpose) {
    return res.status(400).json({ error: 'معرف المستفيد والمبلغ والغرض مطلوبة' });
  }
  
  if (amount <= 0) {
    return res.status(400).json({ error: 'المبلغ يجب أن يكون أكبر من صفر' });
  }
  
  // التحقق من عدم وجود سلف نشطة للمستفيد
  const checkQuery = 'SELECT COUNT(*) as count FROM loans WHERE member_id = ? AND status IN ("pending", "approved", "disbursed")';
  
  db.get(checkQuery, [member_id], (err, result) => {
    if (err) {
      console.error('خطأ في التحقق من السلف:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    if (result.count > 0) {
      return res.status(400).json({ error: 'يوجد سلفة نشطة للمستفيد' });
    }
    
    const query = `
      INSERT INTO loans (
        member_id, amount, purpose, request_date, due_date, 
        monthly_deduction, remaining_balance, created_by
      ) VALUES (?, ?, ?, CURRENT_DATE, ?, ?, ?, ?)
    `;
    
    const params = [member_id, amount, purpose, due_date, monthly_deduction, amount, req.user.id];
    
    db.run(query, params, function(err) {
      if (err) {
        console.error('خطأ في إضافة السلفة:', err);
        return res.status(500).json({ error: 'خطأ في إضافة السلفة' });
      }
      
      res.status(201).json({
        message: 'تم تقديم طلب السلفة بنجاح',
        loan: { id: this.lastID, ...req.body, status: 'pending' }
      });
    });
  });
});

// الموافقة على سلفة
router.patch('/:id/approve', authenticateToken, requireTreasurer, (req, res) => {
  const { notes } = req.body;
  
  const query = `
    UPDATE loans SET 
      status = 'approved', 
      approved_by = ?, 
      approval_date = CURRENT_DATE,
      notes = COALESCE(?, notes)
    WHERE id = ? AND status = 'pending'
  `;
  
  db.run(query, [req.user.id, notes, req.params.id], function(err) {
    if (err) {
      console.error('خطأ في الموافقة على السلفة:', err);
      return res.status(500).json({ error: 'خطأ في الموافقة على السلفة' });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'السلفة غير موجودة أو تم الموافقة عليها مسبقاً' });
    }
    
    res.json({ message: 'تم الموافقة على السلفة بنجاح' });
  });
});

// رفض سلفة
router.patch('/:id/reject', authenticateToken, requireTreasurer, (req, res) => {
  const { notes } = req.body;
  
  if (!notes) {
    return res.status(400).json({ error: 'سبب الرفض مطلوب' });
  }
  
  const query = `
    UPDATE loans SET 
      status = 'rejected', 
      approved_by = ?, 
      notes = ?
    WHERE id = ? AND status = 'pending'
  `;
  
  db.run(query, [req.user.id, notes, req.params.id], function(err) {
    if (err) {
      console.error('خطأ في رفض السلفة:', err);
      return res.status(500).json({ error: 'خطأ في رفض السلفة' });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'السلفة غير موجودة أو تم التعامل معها مسبقاً' });
    }
    
    res.json({ message: 'تم رفض السلفة' });
  });
});

// صرف سلفة
router.patch('/:id/disburse', authenticateToken, requireTreasurer, (req, res) => {
  const { notes } = req.body;
  
  const query = `
    UPDATE loans SET 
      status = 'disbursed', 
      disbursed_by = ?,
      notes = COALESCE(?, notes)
    WHERE id = ? AND status = 'approved'
  `;
  
  db.run(query, [req.user.id, notes, req.params.id], function(err) {
    if (err) {
      console.error('خطأ في صرف السلفة:', err);
      return res.status(500).json({ error: 'خطأ في صرف السلفة' });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'السلفة غير موجودة أو غير موافق عليها' });
    }
    
    res.json({ message: 'تم صرف السلفة بنجاح' });
  });
});

// إضافة دفعة سداد
router.post('/:id/payments', authenticateToken, requireTreasurer, (req, res) => {
  const { amount, payment_date, payment_method, receipt_number, notes } = req.body;
  const loan_id = req.params.id;
  
  if (!amount || !payment_date) {
    return res.status(400).json({ error: 'المبلغ وتاريخ الدفع مطلوبان' });
  }
  
  if (amount <= 0) {
    return res.status(400).json({ error: 'المبلغ يجب أن يكون أكبر من صفر' });
  }
  
  // الحصول على بيانات السلفة
  const loanQuery = 'SELECT * FROM loans WHERE id = ? AND status = "disbursed"';
  
  db.get(loanQuery, [loan_id], (err, loan) => {
    if (err) {
      console.error('خطأ في قاعدة البيانات:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    if (!loan) {
      return res.status(404).json({ error: 'السلفة غير موجودة أو غير مصروفة' });
    }
    
    if (amount > loan.remaining_balance) {
      return res.status(400).json({ error: 'المبلغ أكبر من الرصيد المتبقي' });
    }
    
    // إضافة الدفعة
    const insertQuery = `
      INSERT INTO loan_payments (loan_id, amount, payment_date, payment_method, receipt_number, notes, created_by)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
    
    const insertParams = [loan_id, amount, payment_date, payment_method, receipt_number, notes, req.user.id];
    
    db.run(insertQuery, insertParams, function(err) {
      if (err) {
        console.error('خطأ في إضافة الدفعة:', err);
        return res.status(500).json({ error: 'خطأ في إضافة الدفعة' });
      }
      
      // تحديث السلفة
      const newTotalRepaid = loan.total_repaid + amount;
      const newRemainingBalance = loan.amount - newTotalRepaid;
      const newStatus = newRemainingBalance <= 0 ? 'repaid' : 'disbursed';
      
      const updateQuery = `
        UPDATE loans SET 
          total_repaid = ?, 
          remaining_balance = ?,
          status = ?
        WHERE id = ?
      `;
      
      db.run(updateQuery, [newTotalRepaid, newRemainingBalance, newStatus, loan_id], function(err) {
        if (err) {
          console.error('خطأ في تحديث السلفة:', err);
          return res.status(500).json({ error: 'خطأ في تحديث السلفة' });
        }
        
        res.status(201).json({
          message: 'تم إضافة الدفعة بنجاح',
          payment: { id: this.lastID, ...req.body },
          loan_status: newStatus,
          remaining_balance: newRemainingBalance
        });
      });
    });
  });
});

module.exports = router;
