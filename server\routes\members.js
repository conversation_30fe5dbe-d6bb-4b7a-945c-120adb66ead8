const express = require('express');
const database = require('../database/database');
const { authenticateToken, requireEmployee, requireTreasurer } = require('../middleware/auth');

const router = express.Router();
const db = database.getConnection();

// الحصول على جميع المستفيدين
router.get('/', authenticateToken, requireEmployee, (req, res) => {
  const { type, search, page = 1, limit = 50 } = req.query;
  const offset = (page - 1) * limit;
  
  let query = 'SELECT * FROM members WHERE 1=1';
  let params = [];
  
  if (type) {
    query += ' AND type = ?';
    params.push(type);
  }
  
  if (search) {
    query += ' AND (full_name LIKE ? OR employee_id LIKE ? OR national_id LIKE ?)';
    const searchTerm = `%${search}%`;
    params.push(searchTerm, searchTerm, searchTerm);
  }
  
  query += ' ORDER BY full_name ASC LIMIT ? OFFSET ?';
  params.push(parseInt(limit), parseInt(offset));
  
  db.all(query, params, (err, members) => {
    if (err) {
      console.error('خطأ في قاعدة البيانات:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    // الحصول على العدد الإجمالي
    let countQuery = 'SELECT COUNT(*) as total FROM members WHERE 1=1';
    let countParams = [];
    
    if (type) {
      countQuery += ' AND type = ?';
      countParams.push(type);
    }
    
    if (search) {
      countQuery += ' AND (full_name LIKE ? OR employee_id LIKE ? OR national_id LIKE ?)';
      const searchTerm = `%${search}%`;
      countParams.push(searchTerm, searchTerm, searchTerm);
    }
    
    db.get(countQuery, countParams, (err, countResult) => {
      if (err) {
        console.error('خطأ في عد المستفيدين:', err);
        return res.status(500).json({ error: 'خطأ في الخادم' });
      }
      
      res.json({
        members,
        pagination: {
          total: countResult.total,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(countResult.total / limit)
        }
      });
    });
  });
});

// الحصول على مستفيد واحد
router.get('/:id', authenticateToken, requireEmployee, (req, res) => {
  const query = 'SELECT * FROM members WHERE id = ?';
  
  db.get(query, [req.params.id], (err, member) => {
    if (err) {
      console.error('خطأ في قاعدة البيانات:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    if (!member) {
      return res.status(404).json({ error: 'المستفيد غير موجود' });
    }
    
    res.json({ member });
  });
});

// إضافة مستفيد جديد
router.post('/', authenticateToken, requireTreasurer, (req, res) => {
  const {
    employee_id, full_name, type, department, position,
    hire_date, retirement_date, phone, email, address,
    national_id, bank_account, emergency_contact, emergency_phone
  } = req.body;
  
  if (!employee_id || !full_name || !type) {
    return res.status(400).json({ error: 'رقم الموظف والاسم ونوع المستفيد مطلوبة' });
  }
  
  const query = `
    INSERT INTO members (
      employee_id, full_name, type, department, position,
      hire_date, retirement_date, phone, email, address,
      national_id, bank_account, emergency_contact, emergency_phone
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;
  
  const params = [
    employee_id, full_name, type, department, position,
    hire_date, retirement_date, phone, email, address,
    national_id, bank_account, emergency_contact, emergency_phone
  ];
  
  db.run(query, params, function(err) {
    if (err) {
      if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        return res.status(400).json({ error: 'رقم الموظف موجود مسبقاً' });
      }
      console.error('خطأ في إضافة المستفيد:', err);
      return res.status(500).json({ error: 'خطأ في إضافة المستفيد' });
    }
    
    res.status(201).json({
      message: 'تم إضافة المستفيد بنجاح',
      member: { id: this.lastID, ...req.body }
    });
  });
});

// تحديث مستفيد
router.put('/:id', authenticateToken, requireTreasurer, (req, res) => {
  const {
    employee_id, full_name, type, department, position,
    hire_date, retirement_date, phone, email, address,
    national_id, bank_account, emergency_contact, emergency_phone, is_active
  } = req.body;
  
  const query = `
    UPDATE members SET
      employee_id = ?, full_name = ?, type = ?, department = ?, position = ?,
      hire_date = ?, retirement_date = ?, phone = ?, email = ?, address = ?,
      national_id = ?, bank_account = ?, emergency_contact = ?, emergency_phone = ?,
      is_active = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `;
  
  const params = [
    employee_id, full_name, type, department, position,
    hire_date, retirement_date, phone, email, address,
    national_id, bank_account, emergency_contact, emergency_phone,
    is_active !== undefined ? is_active : 1, req.params.id
  ];
  
  db.run(query, params, function(err) {
    if (err) {
      if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        return res.status(400).json({ error: 'رقم الموظف موجود مسبقاً' });
      }
      console.error('خطأ في تحديث المستفيد:', err);
      return res.status(500).json({ error: 'خطأ في تحديث المستفيد' });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'المستفيد غير موجود' });
    }
    
    res.json({ message: 'تم تحديث المستفيد بنجاح' });
  });
});

// حذف مستفيد (إلغاء تفعيل)
router.delete('/:id', authenticateToken, requireTreasurer, (req, res) => {
  const query = 'UPDATE members SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
  
  db.run(query, [req.params.id], function(err) {
    if (err) {
      console.error('خطأ في حذف المستفيد:', err);
      return res.status(500).json({ error: 'خطأ في حذف المستفيد' });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'المستفيد غير موجود' });
    }
    
    res.json({ message: 'تم حذف المستفيد بنجاح' });
  });
});

module.exports = router;
