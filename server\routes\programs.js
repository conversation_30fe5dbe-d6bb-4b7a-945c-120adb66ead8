const express = require('express');
const database = require('../database/database');
const { authenticateToken, requireEmployee, requireTreasurer } = require('../middleware/auth');

const router = express.Router();
const db = database.getConnection();

// الحصول على جميع البرامج
router.get('/', authenticateToken, requireEmployee, (req, res) => {
  const { type, status, year, page = 1, limit = 20 } = req.query;
  const offset = (page - 1) * limit;
  
  let query = `
    SELECT p.*, u.full_name as created_by_name,
           COUNT(pp.id) as participants_count,
           SUM(pp.amount_paid) as total_collected
    FROM programs p
    LEFT JOIN users u ON p.created_by = u.id
    LEFT JOIN program_participants pp ON p.id = pp.program_id AND pp.status != 'cancelled'
    WHERE 1=1
  `;
  let params = [];
  
  if (type) {
    query += ' AND p.type = ?';
    params.push(type);
  }
  
  if (status) {
    query += ' AND p.status = ?';
    params.push(status);
  }
  
  if (year) {
    query += ' AND strftime("%Y", p.start_date) = ?';
    params.push(year);
  }
  
  query += ' GROUP BY p.id ORDER BY p.start_date DESC LIMIT ? OFFSET ?';
  params.push(parseInt(limit), parseInt(offset));
  
  db.all(query, params, (err, programs) => {
    if (err) {
      console.error('خطأ في قاعدة البيانات:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    res.json({ programs });
  });
});

// الحصول على برنامج واحد مع المشاركين
router.get('/:id', authenticateToken, requireEmployee, (req, res) => {
  const programQuery = 'SELECT * FROM programs WHERE id = ?';
  
  db.get(programQuery, [req.params.id], (err, program) => {
    if (err) {
      console.error('خطأ في قاعدة البيانات:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    if (!program) {
      return res.status(404).json({ error: 'البرنامج غير موجود' });
    }
    
    // الحصول على المشاركين
    const participantsQuery = `
      SELECT pp.*, m.full_name, m.employee_id, m.phone
      FROM program_participants pp
      JOIN members m ON pp.member_id = m.id
      WHERE pp.program_id = ?
      ORDER BY pp.registration_date
    `;
    
    db.all(participantsQuery, [req.params.id], (err, participants) => {
      if (err) {
        console.error('خطأ في الحصول على المشاركين:', err);
        return res.status(500).json({ error: 'خطأ في الخادم' });
      }
      
      res.json({ program, participants });
    });
  });
});

// إضافة برنامج جديد
router.post('/', authenticateToken, requireTreasurer, (req, res) => {
  const {
    name, type, description, start_date, end_date, location,
    max_participants, cost_per_person, total_budget
  } = req.body;
  
  if (!name || !type || !start_date || !end_date) {
    return res.status(400).json({ error: 'اسم البرنامج ونوعه وتاريخ البداية والنهاية مطلوبة' });
  }
  
  const query = `
    INSERT INTO programs (
      name, type, description, start_date, end_date, location,
      max_participants, cost_per_person, total_budget, created_by
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;
  
  const params = [
    name, type, description, start_date, end_date, location,
    max_participants, cost_per_person, total_budget, req.user.id
  ];
  
  db.run(query, params, function(err) {
    if (err) {
      console.error('خطأ في إضافة البرنامج:', err);
      return res.status(500).json({ error: 'خطأ في إضافة البرنامج' });
    }
    
    res.status(201).json({
      message: 'تم إضافة البرنامج بنجاح',
      program: { id: this.lastID, ...req.body }
    });
  });
});

// تسجيل مشارك في برنامج
router.post('/:id/register', authenticateToken, requireEmployee, (req, res) => {
  const { member_id, notes } = req.body;
  const program_id = req.params.id;
  
  if (!member_id) {
    return res.status(400).json({ error: 'معرف المستفيد مطلوب' });
  }
  
  // التحقق من وجود البرنامج وحالته
  const programQuery = 'SELECT * FROM programs WHERE id = ?';
  
  db.get(programQuery, [program_id], (err, program) => {
    if (err) {
      console.error('خطأ في قاعدة البيانات:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    if (!program) {
      return res.status(404).json({ error: 'البرنامج غير موجود' });
    }
    
    if (program.status !== 'registration_open') {
      return res.status(400).json({ error: 'التسجيل في هذا البرنامج غير متاح حالياً' });
    }
    
    // التحقق من عدد المشاركين
    if (program.max_participants) {
      const countQuery = 'SELECT COUNT(*) as count FROM program_participants WHERE program_id = ? AND status != "cancelled"';
      
      db.get(countQuery, [program_id], (err, countResult) => {
        if (err) {
          console.error('خطأ في عد المشاركين:', err);
          return res.status(500).json({ error: 'خطأ في الخادم' });
        }
        
        if (countResult.count >= program.max_participants) {
          return res.status(400).json({ error: 'تم الوصول للحد الأقصى من المشاركين' });
        }
        
        // إضافة المشارك
        registerParticipant();
      });
    } else {
      registerParticipant();
    }
    
    function registerParticipant() {
      const insertQuery = `
        INSERT INTO program_participants (program_id, member_id, notes)
        VALUES (?, ?, ?)
      `;
      
      db.run(insertQuery, [program_id, member_id, notes], function(err) {
        if (err) {
          if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
            return res.status(400).json({ error: 'المستفيد مسجل مسبقاً في هذا البرنامج' });
          }
          console.error('خطأ في تسجيل المشارك:', err);
          return res.status(500).json({ error: 'خطأ في تسجيل المشارك' });
        }
        
        res.status(201).json({
          message: 'تم تسجيل المشارك بنجاح',
          participant: { id: this.lastID, program_id, member_id, notes }
        });
      });
    }
  });
});

// تحديث حالة مشارك
router.patch('/:id/participants/:participantId', authenticateToken, requireTreasurer, (req, res) => {
  const { status, payment_status, amount_paid, notes } = req.body;
  
  const query = `
    UPDATE program_participants SET
      status = COALESCE(?, status),
      payment_status = COALESCE(?, payment_status),
      amount_paid = COALESCE(?, amount_paid),
      notes = COALESCE(?, notes)
    WHERE id = ? AND program_id = ?
  `;
  
  const params = [status, payment_status, amount_paid, notes, req.params.participantId, req.params.id];
  
  db.run(query, params, function(err) {
    if (err) {
      console.error('خطأ في تحديث المشارك:', err);
      return res.status(500).json({ error: 'خطأ في تحديث المشارك' });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'المشارك غير موجود' });
    }
    
    res.json({ message: 'تم تحديث بيانات المشارك بنجاح' });
  });
});

// تحديث حالة البرنامج
router.patch('/:id/status', authenticateToken, requireTreasurer, (req, res) => {
  const { status } = req.body;
  
  if (!status) {
    return res.status(400).json({ error: 'حالة البرنامج مطلوبة' });
  }
  
  const query = 'UPDATE programs SET status = ? WHERE id = ?';
  
  db.run(query, [status, req.params.id], function(err) {
    if (err) {
      console.error('خطأ في تحديث حالة البرنامج:', err);
      return res.status(500).json({ error: 'خطأ في تحديث حالة البرنامج' });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'البرنامج غير موجود' });
    }
    
    res.json({ message: 'تم تحديث حالة البرنامج بنجاح' });
  });
});

// حذف برنامج
router.delete('/:id', authenticateToken, requireTreasurer, (req, res) => {
  // التحقق من عدم وجود مشاركين
  const checkQuery = 'SELECT COUNT(*) as count FROM program_participants WHERE program_id = ?';
  
  db.get(checkQuery, [req.params.id], (err, result) => {
    if (err) {
      console.error('خطأ في التحقق من المشاركين:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    if (result.count > 0) {
      return res.status(400).json({ error: 'لا يمكن حذف برنامج يحتوي على مشاركين' });
    }
    
    const deleteQuery = 'DELETE FROM programs WHERE id = ?';
    
    db.run(deleteQuery, [req.params.id], function(err) {
      if (err) {
        console.error('خطأ في حذف البرنامج:', err);
        return res.status(500).json({ error: 'خطأ في حذف البرنامج' });
      }
      
      if (this.changes === 0) {
        return res.status(404).json({ error: 'البرنامج غير موجود' });
      }
      
      res.json({ message: 'تم حذف البرنامج بنجاح' });
    });
  });
});

module.exports = router;
