const express = require('express');
const database = require('../database/database');
const { authenticateToken, requireEmployee } = require('../middleware/auth');

const router = express.Router();
const db = database.getConnection();

// تقرير مالي شامل
router.get('/financial', authenticateToken, requireEmployee, (req, res) => {
  const { start_date, end_date, year = new Date().getFullYear() } = req.query;
  
  let dateCondition = '';
  let params = [];
  
  if (start_date && end_date) {
    dateCondition = 'WHERE date BETWEEN ? AND ?';
    params = [start_date, end_date];
  } else {
    dateCondition = 'WHERE strftime("%Y", date) = ?';
    params = [year.toString()];
  }
  
  // إجمالي الدخل
  const incomeQuery = `
    SELECT 
      type,
      COUNT(*) as count,
      SUM(amount) as total_amount
    FROM income 
    ${dateCondition}
    GROUP BY type
  `;
  
  // إجمالي المصروفات
  const expensesQuery = `
    SELECT 
      type,
      COUNT(*) as count,
      SUM(amount) as total_amount
    FROM expenses 
    ${dateCondition.replace('date', 'date')} AND status = 'paid'
    GROUP BY type
  `;
  
  // تنفيذ الاستعلامات
  db.all(incomeQuery, params, (err, incomeData) => {
    if (err) {
      console.error('خطأ في تقرير الدخل:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    db.all(expensesQuery, params, (err, expensesData) => {
      if (err) {
        console.error('خطأ في تقرير المصروفات:', err);
        return res.status(500).json({ error: 'خطأ في الخادم' });
      }
      
      // حساب الإجماليات
      const totalIncome = incomeData.reduce((sum, item) => sum + item.total_amount, 0);
      const totalExpenses = expensesData.reduce((sum, item) => sum + item.total_amount, 0);
      const netBalance = totalIncome - totalExpenses;
      
      res.json({
        period: start_date && end_date ? `${start_date} إلى ${end_date}` : `سنة ${year}`,
        income: {
          by_type: incomeData,
          total: totalIncome
        },
        expenses: {
          by_type: expensesData,
          total: totalExpenses
        },
        summary: {
          total_income: totalIncome,
          total_expenses: totalExpenses,
          net_balance: netBalance
        }
      });
    });
  });
});

// تقرير المستفيدين
router.get('/members', authenticateToken, requireEmployee, (req, res) => {
  const { type } = req.query;
  
  let query = `
    SELECT 
      type,
      COUNT(*) as count,
      COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_count,
      COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive_count
    FROM members
  `;
  let params = [];
  
  if (type) {
    query += ' WHERE type = ?';
    params.push(type);
  }
  
  query += ' GROUP BY type';
  
  db.all(query, params, (err, memberStats) => {
    if (err) {
      console.error('خطأ في تقرير المستفيدين:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    // إحصائيات إضافية
    const totalQuery = `
      SELECT 
        COUNT(*) as total_members,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as total_active,
        COUNT(CASE WHEN type = 'employee' THEN 1 END) as employees,
        COUNT(CASE WHEN type = 'retired' THEN 1 END) as retired,
        COUNT(CASE WHEN type = 'beneficiary' THEN 1 END) as beneficiaries
      FROM members
    `;
    
    db.get(totalQuery, [], (err, totals) => {
      if (err) {
        console.error('خطأ في إجماليات المستفيدين:', err);
        return res.status(500).json({ error: 'خطأ في الخادم' });
      }
      
      res.json({
        by_type: memberStats,
        totals: totals
      });
    });
  });
});

// تقرير البرامج
router.get('/programs', authenticateToken, requireEmployee, (req, res) => {
  const { year = new Date().getFullYear() } = req.query;
  
  const query = `
    SELECT 
      p.type,
      COUNT(p.id) as programs_count,
      COUNT(pp.id) as total_participants,
      SUM(p.total_budget) as total_budget,
      SUM(pp.amount_paid) as total_collected,
      AVG(CASE WHEN p.max_participants > 0 THEN 
        (COUNT(pp.id) * 100.0 / p.max_participants) 
        ELSE 0 END) as avg_occupancy_rate
    FROM programs p
    LEFT JOIN program_participants pp ON p.id = pp.program_id AND pp.status != 'cancelled'
    WHERE strftime('%Y', p.start_date) = ?
    GROUP BY p.type
  `;
  
  db.all(query, [year.toString()], (err, programStats) => {
    if (err) {
      console.error('خطأ في تقرير البرامج:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    // البرامج الأكثر شعبية
    const popularQuery = `
      SELECT 
        p.name,
        p.type,
        COUNT(pp.id) as participants_count,
        p.max_participants,
        (COUNT(pp.id) * 100.0 / NULLIF(p.max_participants, 0)) as occupancy_rate
      FROM programs p
      LEFT JOIN program_participants pp ON p.id = pp.program_id AND pp.status != 'cancelled'
      WHERE strftime('%Y', p.start_date) = ?
      GROUP BY p.id
      ORDER BY participants_count DESC
      LIMIT 10
    `;
    
    db.all(popularQuery, [year.toString()], (err, popularPrograms) => {
      if (err) {
        console.error('خطأ في البرامج الشعبية:', err);
        return res.status(500).json({ error: 'خطأ في الخادم' });
      }
      
      res.json({
        year: parseInt(year),
        by_type: programStats,
        most_popular: popularPrograms
      });
    });
  });
});

// تقرير السلف
router.get('/loans', authenticateToken, requireEmployee, (req, res) => {
  const { year = new Date().getFullYear() } = req.query;
  
  const query = `
    SELECT 
      status,
      COUNT(*) as count,
      SUM(amount) as total_amount,
      SUM(total_repaid) as total_repaid,
      SUM(remaining_balance) as total_remaining,
      AVG(amount) as avg_amount
    FROM loans
    WHERE strftime('%Y', request_date) = ?
    GROUP BY status
  `;
  
  db.all(query, [year.toString()], (err, loanStats) => {
    if (err) {
      console.error('خطأ في تقرير السلف:', err);
      return res.status(500).json({ error: 'خطأ في الخادم' });
    }
    
    // السلف المتأخرة
    const overdueQuery = `
      SELECT 
        l.id,
        l.amount,
        l.remaining_balance,
        l.due_date,
        m.full_name,
        m.employee_id,
        julianday('now') - julianday(l.due_date) as days_overdue
      FROM loans l
      JOIN members m ON l.member_id = m.id
      WHERE l.status = 'disbursed' 
        AND l.due_date < date('now')
        AND l.remaining_balance > 0
      ORDER BY days_overdue DESC
    `;
    
    db.all(overdueQuery, [], (err, overdueLoans) => {
      if (err) {
        console.error('خطأ في السلف المتأخرة:', err);
        return res.status(500).json({ error: 'خطأ في الخادم' });
      }
      
      res.json({
        year: parseInt(year),
        by_status: loanStats,
        overdue_loans: overdueLoans
      });
    });
  });
});

// تقرير شهري مفصل
router.get('/monthly/:year/:month', authenticateToken, requireEmployee, (req, res) => {
  const { year, month } = req.params;
  
  // التحقق من صحة المدخلات
  if (!year || !month || month < 1 || month > 12) {
    return res.status(400).json({ error: 'السنة والشهر مطلوبان وصحيحان' });
  }
  
  const monthStr = month.toString().padStart(2, '0');
  const dateCondition = `strftime('%Y-%m', date) = '${year}-${monthStr}'`;
  
  // الدخل الشهري
  const incomeQuery = `
    SELECT type, SUM(amount) as amount, COUNT(*) as count
    FROM income 
    WHERE ${dateCondition}
    GROUP BY type
  `;
  
  // المصروفات الشهرية
  const expensesQuery = `
    SELECT type, SUM(amount) as amount, COUNT(*) as count
    FROM expenses 
    WHERE ${dateCondition} AND status = 'paid'
    GROUP BY type
  `;
  
  // البرامج النشطة
  const programsQuery = `
    SELECT p.name, p.type, COUNT(pp.id) as participants
    FROM programs p
    LEFT JOIN program_participants pp ON p.id = pp.program_id
    WHERE (strftime('%Y-%m', p.start_date) <= '${year}-${monthStr}' 
           AND strftime('%Y-%m', p.end_date) >= '${year}-${monthStr}')
    GROUP BY p.id
  `;
  
  // تنفيذ الاستعلامات
  Promise.all([
    new Promise((resolve, reject) => {
      db.all(incomeQuery, [], (err, data) => err ? reject(err) : resolve(data));
    }),
    new Promise((resolve, reject) => {
      db.all(expensesQuery, [], (err, data) => err ? reject(err) : resolve(data));
    }),
    new Promise((resolve, reject) => {
      db.all(programsQuery, [], (err, data) => err ? reject(err) : resolve(data));
    })
  ]).then(([income, expenses, programs]) => {
    const totalIncome = income.reduce((sum, item) => sum + item.amount, 0);
    const totalExpenses = expenses.reduce((sum, item) => sum + item.amount, 0);
    
    res.json({
      period: `${year}-${monthStr}`,
      income: {
        items: income,
        total: totalIncome
      },
      expenses: {
        items: expenses,
        total: totalExpenses
      },
      programs: programs,
      summary: {
        net_balance: totalIncome - totalExpenses,
        total_income: totalIncome,
        total_expenses: totalExpenses
      }
    });
  }).catch(err => {
    console.error('خطأ في التقرير الشهري:', err);
    res.status(500).json({ error: 'خطأ في الخادم' });
  });
});

module.exports = router;
