const bcrypt = require('bcryptjs');
const database = require('../database/database');

const db = database.getConnection();

async function initializeDatabase() {
  console.log('🔄 بدء تهيئة قاعدة البيانات...');
  
  try {
    // إنشاء مستخدم إداري افتراضي
    const adminPassword = await bcrypt.hash('admin123', 10);
    
    const insertAdmin = `
      INSERT OR IGNORE INTO users (username, password, full_name, role, email)
      VALUES ('admin', ?, 'المدير العام', 'admin', '<EMAIL>')
    `;
    
    db.run(insertAdmin, [adminPassword], function(err) {
      if (err) {
        console.error('خطأ في إنشاء المدير:', err);
      } else {
        console.log('✅ تم إنشاء المستخدم الإداري (admin/admin123)');
      }
    });
    
    // إنشاء أمين مال افتراضي
    const treasurerPassword = await bcrypt.hash('treasurer123', 10);
    
    const insertTreasurer = `
      INSERT OR IGNORE INTO users (username, password, full_name, role, email)
      VALUES ('treasurer', ?, 'أمين المال', 'treasurer', '<EMAIL>')
    `;
    
    db.run(insertTreasurer, [treasurerPassword], function(err) {
      if (err) {
        console.error('خطأ في إنشاء أمين المال:', err);
      } else {
        console.log('✅ تم إنشاء أمين المال (treasurer/treasurer123)');
      }
    });
    
    // إنشاء موظف افتراضي
    const employeePassword = await bcrypt.hash('employee123', 10);
    
    const insertEmployee = `
      INSERT OR IGNORE INTO users (username, password, full_name, role, email)
      VALUES ('employee', ?, 'موظف النظام', 'employee', '<EMAIL>')
    `;
    
    db.run(insertEmployee, [employeePassword], function(err) {
      if (err) {
        console.error('خطأ في إنشاء الموظف:', err);
      } else {
        console.log('✅ تم إنشاء الموظف (employee/employee123)');
      }
    });
    
    // إضافة بعض المستفيدين التجريبيين
    const sampleMembers = [
      {
        employee_id: 'EMP001',
        full_name: 'أحمد محمد علي',
        type: 'employee',
        department: 'الإدارة',
        position: 'مدير',
        hire_date: '2020-01-15',
        phone: '0123456789',
        email: '<EMAIL>'
      },
      {
        employee_id: 'EMP002',
        full_name: 'فاطمة أحمد حسن',
        type: 'employee',
        department: 'المحاسبة',
        position: 'محاسبة',
        hire_date: '2019-03-10',
        phone: '0123456790',
        email: '<EMAIL>'
      },
      {
        employee_id: 'RET001',
        full_name: 'محمد عبد الله',
        type: 'retired',
        department: 'الهندسة',
        position: 'مهندس أول',
        hire_date: '1990-05-01',
        retirement_date: '2022-05-01',
        phone: '0123456791'
      }
    ];
    
    const insertMember = `
      INSERT OR IGNORE INTO members (
        employee_id, full_name, type, department, position, 
        hire_date, retirement_date, phone, email
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    sampleMembers.forEach(member => {
      db.run(insertMember, [
        member.employee_id, member.full_name, member.type,
        member.department, member.position, member.hire_date,
        member.retirement_date, member.phone, member.email
      ], function(err) {
        if (err) {
          console.error(`خطأ في إضافة المستفيد ${member.full_name}:`, err);
        } else {
          console.log(`✅ تم إضافة المستفيد: ${member.full_name}`);
        }
      });
    });
    
    // إضافة بعض مصادر الدخل التجريبية
    const sampleIncome = [
      {
        type: 'subscription',
        amount: 50000,
        description: 'اشتراكات الموظفين - يناير 2025',
        source: 'اشتراكات شهرية',
        date: '2025-01-01'
      },
      {
        type: 'government_aid',
        amount: 100000,
        description: 'إعانة الدولة - الربع الأول',
        source: 'وزارة العمل',
        date: '2025-01-15'
      },
      {
        type: 'bank_interest',
        amount: 5000,
        description: 'فوائد بنكية',
        source: 'البنك الأهلي',
        date: '2025-01-31'
      }
    ];
    
    const insertIncome = `
      INSERT OR IGNORE INTO income (type, amount, description, source, date, created_by)
      VALUES (?, ?, ?, ?, ?, 1)
    `;
    
    sampleIncome.forEach(income => {
      db.run(insertIncome, [
        income.type, income.amount, income.description,
        income.source, income.date
      ], function(err) {
        if (err) {
          console.error('خطأ في إضافة الدخل:', err);
        } else {
          console.log(`✅ تم إضافة دخل: ${income.description}`);
        }
      });
    });
    
    // إضافة برنامج تجريبي
    const sampleProgram = {
      name: 'برنامج الاصطياف الصيفي 2025',
      type: 'summer',
      description: 'برنامج اصطياف للموظفين وعائلاتهم',
      start_date: '2025-07-01',
      end_date: '2025-07-15',
      location: 'الساحل الشمالي',
      max_participants: 50,
      cost_per_person: 2000,
      total_budget: 100000,
      status: 'planning'
    };
    
    const insertProgram = `
      INSERT OR IGNORE INTO programs (
        name, type, description, start_date, end_date, location,
        max_participants, cost_per_person, total_budget, status, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
    `;
    
    db.run(insertProgram, [
      sampleProgram.name, sampleProgram.type, sampleProgram.description,
      sampleProgram.start_date, sampleProgram.end_date, sampleProgram.location,
      sampleProgram.max_participants, sampleProgram.cost_per_person,
      sampleProgram.total_budget, sampleProgram.status
    ], function(err) {
      if (err) {
        console.error('خطأ في إضافة البرنامج:', err);
      } else {
        console.log('✅ تم إضافة برنامج تجريبي');
      }
    });
    
    console.log('\n🎉 تم الانتهاء من تهيئة قاعدة البيانات بنجاح!');
    console.log('\n📋 بيانات الدخول الافتراضية:');
    console.log('المدير: admin / admin123');
    console.log('أمين المال: treasurer / treasurer123');
    console.log('الموظف: employee / employee123');
    
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
  }
}

// تشغيل التهيئة
initializeDatabase();

module.exports = initializeDatabase;
